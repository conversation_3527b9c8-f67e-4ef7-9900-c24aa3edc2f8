<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Test Send Transaction Flow</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        max-width: 600px;
        margin: 0 auto;
        padding: 20px;
        background-color: #f5f5f5;
      }
      .container {
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }
      .test-section {
        margin: 20px 0;
        padding: 15px;
        border: 1px solid #ddd;
        border-radius: 5px;
      }
      button {
        background: #007cba;
        color: white;
        border: none;
        padding: 10px 15px;
        border-radius: 5px;
        cursor: pointer;
        margin: 5px;
      }
      button:hover {
        background: #005a87;
      }
      .result {
        margin-top: 10px;
        padding: 10px;
        background: #f8f9fa;
        border-radius: 3px;
        font-family: monospace;
        white-space: pre-wrap;
        max-height: 200px;
        overflow-y: auto;
      }
      .success {
        border-left: 4px solid #28a745;
      }
      .error {
        border-left: 4px solid #dc3545;
      }
      .info {
        border-left: 4px solid #17a2b8;
      }
      .status {
        margin: 10px 0;
        padding: 10px;
        border-radius: 5px;
      }
      .connected {
        background: #d4edda;
        color: #155724;
      }
      .disconnected {
        background: #f8d7da;
        color: #721c24;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>🐱 Test Send Transaction Flow</h1>

      <div id="status" class="status disconnected">Status: Not connected</div>

      <div class="test-section">
        <h3>1. Connect Wallet</h3>
        <button onclick="connectWallet()">Connect Wallet</button>
        <div id="connection-result" class="result"></div>
      </div>

      <div class="test-section">
        <h3>2. Test Send Transaction (Should Show Popup Even if Locked)</h3>
        <p>
          This test will call eth_sendTransaction. The popup should appear
          immediately, even if the wallet is locked. The popup will handle
          unlock.
        </p>
        <button onclick="testSendTransaction()">Send Transaction</button>
        <div id="transaction-result" class="result"></div>
      </div>

      <div class="test-section">
        <h3>3. Test RPC Methods</h3>
        <button onclick="testGasPrice()">Get Gas Price</button>
        <button onclick="testEstimateGas()">Estimate Gas</button>
        <button onclick="testCall()">Contract Call</button>
        <button onclick="testBlockNumber()">Block Number</button>
        <button onclick="testGetTransactionByHash()">
          Get Transaction By Hash
        </button>
        <div id="rpc-result" class="result"></div>
      </div>
    </div>

    <script>
      let ethereum;
      let accounts = [];

      // Initialize
      window.addEventListener('load', async () => {
        if (typeof window.ethereum !== 'undefined') {
          ethereum = window.ethereum;
          updateStatus('Purro extension detected');

          // Listen for account changes
          ethereum.on('accountsChanged', accounts => {
            console.log('Accounts changed:', accounts);
            updateConnectionStatus(accounts);
          });

          // Listen for chain changes
          ethereum.on('chainChanged', chainId => {
            console.log('Chain changed:', chainId);
          });
        } else {
          updateStatus(
            'No Ethereum provider found. Please install Purro extension.'
          );
        }
      });

      function updateStatus(message) {
        const statusEl = document.getElementById('status');
        statusEl.textContent = `Status: ${message}`;
      }

      function updateConnectionStatus(accountList) {
        const statusEl = document.getElementById('status');
        if (accountList && accountList.length > 0) {
          accounts = accountList;
          statusEl.className = 'status connected';
          statusEl.textContent = `Connected: ${accountList[0]}`;
        } else {
          accounts = [];
          statusEl.className = 'status disconnected';
          statusEl.textContent = 'Status: Not connected';
        }
      }

      function displayResult(elementId, result, type = 'info') {
        const resultEl = document.getElementById(elementId);
        resultEl.className = `result ${type}`;
        resultEl.textContent =
          typeof result === 'object' ? JSON.stringify(result, null, 2) : result;
      }

      async function connectWallet() {
        try {
          const accounts = await ethereum.request({
            method: 'eth_requestAccounts',
          });
          updateConnectionStatus(accounts);
          displayResult(
            'connection-result',
            `Connected: ${accounts[0]}`,
            'success'
          );
        } catch (error) {
          displayResult(
            'connection-result',
            `Error: ${error.message}`,
            'error'
          );
        }
      }

      async function testSendTransaction() {
        try {
          if (!accounts.length) {
            throw new Error('Please connect wallet first');
          }

          displayResult(
            'transaction-result',
            'Calling eth_sendTransaction...\nPopup should appear immediately, even if wallet is locked.',
            'info'
          );

          const txHash = await ethereum.request({
            method: 'eth_sendTransaction',
            params: [
              {
                to: '******************************************',
                value: '0x1', // 1 wei
              },
            ],
          });

          // Validate transaction hash format
          const isValidHash =
            typeof txHash === 'string' &&
            txHash.startsWith('0x') &&
            txHash.length === 66;

          displayResult(
            'transaction-result',
            `✅ Transaction sent successfully!\n` +
              `Hash: ${txHash}\n` +
              `Type: ${typeof txHash}\n` +
              `Valid format: ${isValidHash ? '✅' : '❌'}\n\n` +
              `This means the popup handled unlock correctly!`,
            'success'
          );
        } catch (error) {
          if (error.message.includes('User rejected')) {
            displayResult(
              'transaction-result',
              `✅ User rejected transaction (expected behavior)\nPopup appeared and worked correctly!`,
              'info'
            );
          } else {
            displayResult(
              'transaction-result',
              `❌ Error: ${error.message}`,
              'error'
            );
          }
        }
      }

      async function testGasPrice() {
        try {
          const gasPrice = await ethereum.request({
            method: 'eth_gasPrice',
          });
          displayResult(
            'rpc-result',
            `Gas Price: ${gasPrice} (${parseInt(gasPrice, 16)} wei)`,
            'success'
          );
        } catch (error) {
          displayResult('rpc-result', `Error: ${error.message}`, 'error');
        }
      }

      async function testEstimateGas() {
        try {
          if (!accounts.length) {
            throw new Error('Please connect wallet first');
          }

          const gasEstimate = await ethereum.request({
            method: 'eth_estimateGas',
            params: [
              {
                from: accounts[0],
                to: '******************************************',
                value: '0x1',
              },
            ],
          });
          displayResult(
            'rpc-result',
            `Gas Estimate: ${gasEstimate} (${parseInt(gasEstimate, 16)})`,
            'success'
          );
        } catch (error) {
          displayResult('rpc-result', `Error: ${error.message}`, 'error');
        }
      }

      async function testCall() {
        try {
          const result = await ethereum.request({
            method: 'eth_call',
            params: [
              {
                to: '******************************************',
                data: '0x70a08231000000000000000000000000742d35Cc6634C0532925a3b8D4C9db96C4b4d8b6',
              },
              'latest',
            ],
          });
          displayResult('rpc-result', `Call Result: ${result}`, 'success');
        } catch (error) {
          displayResult('rpc-result', `Error: ${error.message}`, 'error');
        }
      }

      async function testBlockNumber() {
        try {
          const blockNumber = await ethereum.request({
            method: 'eth_blockNumber',
          });
          displayResult(
            'rpc-result',
            `Block Number: ${blockNumber} (${parseInt(blockNumber, 16)})`,
            'success'
          );
        } catch (error) {
          displayResult('rpc-result', `Error: ${error.message}`, 'error');
        }
      }

      async function testGetTransactionByHash() {
        try {
          // Use a sample transaction hash (you can replace with a real one)
          const sampleTxHash =
            '0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef';

          const transaction = await ethereum.request({
            method: 'eth_getTransactionByHash',
            params: [sampleTxHash],
          });

          displayResult(
            'rpc-result',
            `Transaction: ${JSON.stringify(transaction, null, 2)}`,
            'success'
          );
        } catch (error) {
          displayResult('rpc-result', `Error: ${error.message}`, 'error');
        }
      }
    </script>
  </body>
</html>
