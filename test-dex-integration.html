<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Purro Extension DEX Integration Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 3px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        .success {
            border-left: 4px solid #28a745;
        }
        .error {
            border-left: 4px solid #dc3545;
        }
        .info {
            border-left: 4px solid #17a2b8;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .connected {
            background: #d4edda;
            color: #155724;
        }
        .disconnected {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🐱 Purro Extension DEX Integration Test</h1>
        
        <div id="status" class="status disconnected">
            Status: Not connected
        </div>

        <div class="test-section">
            <h3>1. Connection</h3>
            <button onclick="connectWallet()">Connect Wallet</button>
            <button onclick="getAccounts()">Get Accounts</button>
            <button onclick="getChainId()">Get Chain ID</button>
            <div id="connection-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>2. RPC Methods (Previously Failing)</h3>
            <button onclick="testEstimateGas()">Test eth_estimateGas</button>
            <button onclick="testCall()">Test eth_call</button>
            <button onclick="testGetBlockNumber()">Test eth_blockNumber</button>
            <div id="rpc-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>3. Signing Methods</h3>
            <button onclick="testPersonalSign()">Test personal_sign</button>
            <button onclick="testSignTypedData()">Test eth_signTypedData</button>
            <button onclick="testSignTransaction()">Test eth_signTransaction</button>
            <div id="signing-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>4. Transaction Methods</h3>
            <button onclick="testSendTransaction()">Test eth_sendTransaction</button>
            <div id="transaction-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>5. DEX Simulation</h3>
            <button onclick="simulateDEXSwap()">Simulate DEX Swap Flow</button>
            <div id="dex-result" class="result"></div>
        </div>
    </div>

    <script>
        let ethereum;
        let accounts = [];
        let chainId;

        // Initialize
        window.addEventListener('load', async () => {
            if (typeof window.ethereum !== 'undefined') {
                ethereum = window.ethereum;
                updateStatus('Purro extension detected');
                
                // Listen for account changes
                ethereum.on('accountsChanged', (accounts) => {
                    console.log('Accounts changed:', accounts);
                    updateConnectionStatus(accounts);
                });

                // Listen for chain changes
                ethereum.on('chainChanged', (chainId) => {
                    console.log('Chain changed:', chainId);
                    window.location.reload();
                });
            } else {
                updateStatus('No Ethereum provider found. Please install Purro extension.');
            }
        });

        function updateStatus(message) {
            const statusEl = document.getElementById('status');
            statusEl.textContent = `Status: ${message}`;
        }

        function updateConnectionStatus(accountList) {
            const statusEl = document.getElementById('status');
            if (accountList && accountList.length > 0) {
                accounts = accountList;
                statusEl.className = 'status connected';
                statusEl.textContent = `Connected: ${accountList[0]}`;
            } else {
                accounts = [];
                statusEl.className = 'status disconnected';
                statusEl.textContent = 'Status: Not connected';
            }
        }

        function displayResult(elementId, result, type = 'info') {
            const resultEl = document.getElementById(elementId);
            resultEl.className = `result ${type}`;
            resultEl.textContent = typeof result === 'object' ? JSON.stringify(result, null, 2) : result;
        }

        async function connectWallet() {
            try {
                const accounts = await ethereum.request({ method: 'eth_requestAccounts' });
                updateConnectionStatus(accounts);
                displayResult('connection-result', `Connected: ${accounts[0]}`, 'success');
            } catch (error) {
                displayResult('connection-result', `Error: ${error.message}`, 'error');
            }
        }

        async function getAccounts() {
            try {
                const accounts = await ethereum.request({ method: 'eth_accounts' });
                updateConnectionStatus(accounts);
                displayResult('connection-result', `Accounts: ${JSON.stringify(accounts)}`, 'success');
            } catch (error) {
                displayResult('connection-result', `Error: ${error.message}`, 'error');
            }
        }

        async function getChainId() {
            try {
                chainId = await ethereum.request({ method: 'eth_chainId' });
                displayResult('connection-result', `Chain ID: ${chainId} (${parseInt(chainId, 16)})`, 'success');
            } catch (error) {
                displayResult('connection-result', `Error: ${error.message}`, 'error');
            }
        }

        async function testEstimateGas() {
            try {
                if (!accounts.length) {
                    throw new Error('Please connect wallet first');
                }
                
                const gasEstimate = await ethereum.request({
                    method: 'eth_estimateGas',
                    params: [{
                        from: accounts[0],
                        to: '******************************************',
                        value: '0x1'
                    }]
                });
                displayResult('rpc-result', `Gas estimate: ${gasEstimate} (${parseInt(gasEstimate, 16)})`, 'success');
            } catch (error) {
                displayResult('rpc-result', `Error: ${error.message}`, 'error');
            }
        }

        async function testCall() {
            try {
                // Test eth_call with a simple contract call
                const result = await ethereum.request({
                    method: 'eth_call',
                    params: [{
                        to: '******************************************',
                        data: '0x70a08231000000000000000000000000742d35Cc6634C0532925a3b8D4C9db96C4b4d8b6'
                    }, 'latest']
                });
                displayResult('rpc-result', `Call result: ${result}`, 'success');
            } catch (error) {
                displayResult('rpc-result', `Error: ${error.message}`, 'error');
            }
        }

        async function testGetBlockNumber() {
            try {
                const blockNumber = await ethereum.request({
                    method: 'eth_blockNumber'
                });
                displayResult('rpc-result', `Block number: ${blockNumber} (${parseInt(blockNumber, 16)})`, 'success');
            } catch (error) {
                displayResult('rpc-result', `Error: ${error.message}`, 'error');
            }
        }

        async function testPersonalSign() {
            try {
                if (!accounts.length) {
                    throw new Error('Please connect wallet first');
                }
                
                const message = 'Hello from Purro DEX test!';
                const signature = await ethereum.request({
                    method: 'personal_sign',
                    params: [message, accounts[0]]
                });
                displayResult('signing-result', `Personal sign successful: ${signature}`, 'success');
            } catch (error) {
                displayResult('signing-result', `Error: ${error.message}`, 'error');
            }
        }

        async function testSignTypedData() {
            try {
                if (!accounts.length) {
                    throw new Error('Please connect wallet first');
                }
                
                const typedData = {
                    types: {
                        EIP712Domain: [
                            { name: 'name', type: 'string' },
                            { name: 'version', type: 'string' },
                            { name: 'chainId', type: 'uint256' }
                        ],
                        Message: [
                            { name: 'content', type: 'string' }
                        ]
                    },
                    primaryType: 'Message',
                    domain: {
                        name: 'Purro Test',
                        version: '1',
                        chainId: parseInt(chainId || '0x1', 16)
                    },
                    message: {
                        content: 'Hello from typed data!'
                    }
                };
                
                const signature = await ethereum.request({
                    method: 'eth_signTypedData_v4',
                    params: [accounts[0], JSON.stringify(typedData)]
                });
                displayResult('signing-result', `Typed data sign successful: ${signature}`, 'success');
            } catch (error) {
                displayResult('signing-result', `Error: ${error.message}`, 'error');
            }
        }

        async function testSignTransaction() {
            try {
                if (!accounts.length) {
                    throw new Error('Please connect wallet first');
                }
                
                const txData = {
                    from: accounts[0],
                    to: '******************************************',
                    value: '0x1',
                    gas: '0x5208'
                };
                
                const signature = await ethereum.request({
                    method: 'eth_signTransaction',
                    params: [txData]
                });
                displayResult('signing-result', `Transaction sign successful: ${signature}`, 'success');
            } catch (error) {
                displayResult('signing-result', `Error: ${error.message}`, 'error');
            }
        }

        async function testSendTransaction() {
            try {
                if (!accounts.length) {
                    throw new Error('Please connect wallet first');
                }
                
                const txHash = await ethereum.request({
                    method: 'eth_sendTransaction',
                    params: [{
                        from: accounts[0],
                        to: '******************************************',
                        value: '0x1'
                    }]
                });
                displayResult('transaction-result', `Transaction sent: ${txHash}`, 'success');
            } catch (error) {
                displayResult('transaction-result', `Error: ${error.message}`, 'error');
            }
        }

        async function simulateDEXSwap() {
            try {
                if (!accounts.length) {
                    throw new Error('Please connect wallet first');
                }
                
                displayResult('dex-result', 'Starting DEX swap simulation...', 'info');
                
                // Step 1: Get block number
                const blockNumber = await ethereum.request({ method: 'eth_blockNumber' });
                console.log('✅ Block number:', blockNumber);
                
                // Step 2: Estimate gas for a swap transaction
                const gasEstimate = await ethereum.request({
                    method: 'eth_estimateGas',
                    params: [{
                        from: accounts[0],
                        to: '******************************************',
                        value: '0x0',
                        data: '0xa9059cbb000000000000000000000000742d35cc6634c0532925a3b8d4c9db96c4b4d8b6000000000000000000000000000000000000000000000000000000000000000a'
                    }]
                });
                console.log('✅ Gas estimate:', gasEstimate);
                
                // Step 3: Make a contract call
                const callResult = await ethereum.request({
                    method: 'eth_call',
                    params: [{
                        to: '******************************************',
                        data: '0x70a08231000000000000000000000000742d35Cc6634C0532925a3b8D4C9db96C4b4d8b6'
                    }, 'latest']
                });
                console.log('✅ Contract call:', callResult);
                
                displayResult('dex-result', 
                    `DEX simulation successful!\n` +
                    `Block: ${blockNumber} (${parseInt(blockNumber, 16)})\n` +
                    `Gas estimate: ${gasEstimate} (${parseInt(gasEstimate, 16)})\n` +
                    `Call result: ${callResult}\n\n` +
                    `All RPC methods working correctly! 🎉`, 
                    'success'
                );
                
            } catch (error) {
                displayResult('dex-result', `DEX simulation failed: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
