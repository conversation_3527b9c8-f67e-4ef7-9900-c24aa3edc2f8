const QueryKeys = {
  SPOT_ASSETS: 'spotAssets',
  SPOT_TOKEN_DETAILS: 'spotTokenDetails',
  SPOT_USER_BALANCE: 'spotUserBalance',
  PERPS_ASSETS: 'perpsAssets',
  PERPS_TOKEN_DETAILS: 'perpsTokenDetails',
  PERPS_USER_BALANCE: 'perpsUserBalance',
  HYPER_EVM_TOKEN_PRICES: 'hyperEvmTokenPrices',
  HYPER_EVM_NFTS_COLLECTIONS: 'hyperEvmNftsCollections',
  HYPER_EVM_NFTS: 'hyperEvmNfts',
  HYPER_EVM_ERC20_TOKENS: 'hyperEvmERC20Tokens',
  HYPER_EVM_NFTS_DETAILS: 'hyperEvmNftsDetails',
  HYPER_EVM_NFT_INSTANCES: 'hyperEvmNftInstances',
  HYPER_EVM_TRANSACTIONS: 'hyperEvmTransactions',
  HYPER_EVM_TOKEN_TRANSFERS: 'hyperEvmTokenTransfers',
  TRENDING_POOLS: 'trendingPools',
  TOP_POOLS_BY_DEX: 'topPoolsByDex',
  SPOT_CANDLE_SNAPSHOT: 'spotCandleSnapshot',
  TOP_POOLS_FOR_A_TOKEN: 'topPoolsForAToken',
  // Alchemy API keys
  ETHEREUM_TOKENS: 'ethereumTokens',
  BASE_TOKENS: 'baseTokens',
  ARBITRUM_TOKENS: 'arbitrumTokens',
  ALL_EVM_TOKENS: 'allEvmTokens',
  // Native balance keys
  ETHEREUM_NATIVE_BALANCE: 'ethereumNativeBalance',
  BASE_NATIVE_BALANCE: 'baseNativeBalance',
  ARBITRUM_NATIVE_BALANCE: 'arbitrumNativeBalance',
  HYPEREVM_NATIVE_BALANCE: 'hyperevmNativeBalance',
  NATIVE_TOKEN_PRICES: 'nativeTokenPrices',
  // LiquidSwap API keys
  LIQUIDSWAP_TOKENS: 'liquidswapTokens',
  LIQUIDSWAP_BALANCES: 'liquidswapBalances',
};

export default QueryKeys;
